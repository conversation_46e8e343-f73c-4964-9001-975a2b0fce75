"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { revalidatePath } from "next/cache";

// Schema for additional user info
const AdditionalUserInfoSchema = z.object({
  position: z.string().optional(),
  employeeCount: z.string().optional(),
  occupation: z.string().optional(),
  industry: z.string().optional(),
  subscriptionPackage: z.string().optional(),
  referralCode: z.string().optional(),
  companyName: z.string().optional(),
  companyUsername: z.string().optional(),
  companyAddress: z.string().optional(),
  companyLogo: z.string().optional(),
  billingAddress: z.string().optional(),
  shippingAddress: z.string().optional(),
  faxNumber: z.string().optional(),
  website: z.string().optional(),
  email: z.string().optional(),
  companyPhone: z.string().optional(),
});

export type AdditionalUserInfoData = z.infer<typeof AdditionalUserInfoSchema>;

/**
 * Get additional user info for the current user
 */
export const getAdditionalUserInfo = async () => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Get additional info
    const additionalInfo = await db.additionalUserInfo.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    return {
      success: true,
      data: {
        position: additionalInfo?.position || null,
        employeeCount: additionalInfo?.employeeCount || null,
        occupation: additionalInfo?.occupation || null,
        industry: additionalInfo?.industry || null,
        subscriptionPackage: additionalInfo?.subscriptionPackage || null,
        referralCode: additionalInfo?.referralCode || null,
        companyName: additionalInfo?.companyName || null,
        companyUsername: additionalInfo?.companyUsername || null,
        companyAddress: additionalInfo?.companyAddress || null,
        companyLogo: additionalInfo?.companyLogo || null,
        billingAddress: additionalInfo?.billingAddress || null,
        shippingAddress: additionalInfo?.shippingAddress || null,
        faxNumber: additionalInfo?.companyFaxNumber || null,
        website: additionalInfo?.companyWebsite || null,
        email: additionalInfo?.companyEmail || null,
        companyPhone: additionalInfo?.companyPhone || null,
      },
    };
  } catch (error) {
    console.error("Error fetching additional user info:", error);
    return { error: "Gagal mengambil informasi tambahan" };
  }
};

/**
 * Update additional user info for the current user
 */
export const updateAdditionalUserInfo = async (
  values: AdditionalUserInfoData
) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Validate input
    const validatedFields = AdditionalUserInfoSchema.safeParse(values);
    if (!validatedFields.success) {
      return { error: "Data tidak valid!" };
    }

    const data = validatedFields.data;

    // Upsert additional user info
    await db.additionalUserInfo.upsert({
      where: {
        userId: session.user.id,
      },
      update: {
        position: data.position || null,
        employeeCount: data.employeeCount || null,
        occupation: data.occupation || null,
        industry: data.industry || null,
        subscriptionPackage: data.subscriptionPackage || null,
        referralCode: data.referralCode || null,
        companyName: data.companyName || null,
        companyUsername: data.companyUsername || null,
        companyAddress: data.companyAddress || null,
        companyLogo: data.companyLogo || null,
        billingAddress: data.billingAddress || null,
        shippingAddress: data.shippingAddress || null,
        companyFaxNumber: data.faxNumber || null,
        companyWebsite: data.website || null,
        companyEmail: data.email || null,
        companyPhone: data.companyPhone || null,
        updatedAt: new Date(),
      },
      create: {
        userId: session.user.id,
        position: data.position || null,
        employeeCount: data.employeeCount || null,
        occupation: data.occupation || null,
        industry: data.industry || null,
        subscriptionPackage: data.subscriptionPackage || null,
        referralCode: data.referralCode || null,
        companyName: data.companyName || null,
        companyUsername: data.companyUsername || null,
        companyAddress: data.companyAddress || null,
        companyLogo: data.companyLogo || null,
        billingAddress: data.billingAddress || null,
        shippingAddress: data.shippingAddress || null,
        companyFaxNumber: data.faxNumber || null,
        companyWebsite: data.website || null,
        companyEmail: data.email || null,
        companyPhone: data.companyPhone || null,
      },
    });

    // Revalidate the profile page
    revalidatePath("/dashboard/settings/profile");

    return { success: "Informasi tambahan berhasil diperbarui!" };
  } catch (error) {
    console.error("Error updating additional user info:", error);
    return { error: "Gagal memperbarui informasi tambahan" };
  }
};
