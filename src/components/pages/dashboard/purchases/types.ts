export interface Product {
  id: string;
  name: string;
}

export interface Supplier {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface PurchaseItem {
  id: string;
  quantity: number;
  costAtPurchase: number;
  purchaseId: string;
  productId: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  unit?: string;
  tax?: string | null;
  discountPercentage?: number | null;
  discountAmount?: number | null;
  product: {
    name: string;
    id?: string;
  };
}

// Company info type for user's company information
export interface CompanyInfo {
  companyName: string | null;
  companyAddress: string | null;
  companyPhone: string | null;
  companyEmail: string | null;
}

export interface Purchase {
  id: string;
  purchaseDate: Date | string;
  totalAmount: number;
  invoiceRef: string | null;
  isDraft: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  userId: string;
  supplierId: string | null;
  supplier: Supplier | null;
  items: PurchaseItem[];
  transactionNumber?: string | null;
  supplierEmail?: string | null;
  billingAddress?: string | null;
  tags?: string[];
  memo?: string | null;
  lampiran?: any[]; // Array of file objects with URL and filename
  paymentDueDate?: Date | string | null;
  transactionDate?: Date | string | null;
  // User relationship
  user?: {
    id: string;
    name: string | null;
  } | null;
  // Company info from AdditionalUserInfo
  companyInfo?: CompanyInfo | null;
  // Warehouse relationship
  warehouseId?: string | null;
  warehouse?: {
    id: string;
    name: string;
  } | null;
}

export interface PurchaseCounts {
  total: number;
  thisMonth: number;
  lastMonth: number;
  today: number;
  pending: number;
  drafts: number;
}

export interface ColumnVisibility {
  id: boolean;
  date: boolean;
  supplier: boolean;
  totalAmount: boolean;
  invoiceRef: boolean;
  itemCount: boolean;
  paymentDueDate: boolean;
  tags: boolean;
  quantity: boolean;
  // Checkbox column is always visible, so we don't need to add it here
}
